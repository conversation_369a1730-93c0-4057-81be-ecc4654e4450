import React from "react";
import { Draggable } from "react-beautiful-dnd";
import { cn } from "@/lib/utils";

interface DraggableSectionProps {
  id: string;
  index: number;
  children: React.ReactNode;
  className?: string;
  dragBackgroundColor?: string;
}

export const DraggableSection: React.FC<DraggableSectionProps> = ({
  id,
  index,
  children,
  className,
  dragBackgroundColor,
}) => {
  return (
    <Draggable draggableId={id} index={index}>
      {(provided, snapshot) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          className={cn(
            "transition-transform duration-200",
            snapshot.isDragging && "scale-102 shadow-lg rotate-1",
            className
          )}
          style={{
            backgroundColor: snapshot.isDragging
              ? dragBackgroundColor
              : undefined,
            ...provided.draggableProps.style,
          }}
        >
          {children}
        </div>
      )}
    </Draggable>
  );
};
